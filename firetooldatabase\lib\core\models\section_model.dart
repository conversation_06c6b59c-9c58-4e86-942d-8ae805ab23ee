import 'package:uuid/uuid.dart';

class SectionModel {
  final String id;
  final String name;
  final String? iconName;
  final int order;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<SubSectionModel> subSections;

  SectionModel({
    String? id,
    required this.name,
    this.iconName,
    required this.order,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<SubSectionModel>? subSections,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now(),
        subSections = subSections ?? [];

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'icon_name': iconName,
      'order_index': order,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory SectionModel.fromMap(Map<String, dynamic> map) {
    return SectionModel(
      id: map['id'],
      name: map['name'],
      iconName: map['icon_name'],
      order: map['order_index'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  SectionModel copyWith({
    String? name,
    String? iconName,
    int? order,
    DateTime? updatedAt,
    List<SubSectionModel>? subSections,
  }) {
    return SectionModel(
      id: id,
      name: name ?? this.name,
      iconName: iconName ?? this.iconName,
      order: order ?? this.order,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      subSections: subSections ?? this.subSections,
    );
  }
}

class SubSectionModel {
  final String id;
  final String sectionId;
  final String name;
  final int order;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool hasUnsavedChanges;

  SubSectionModel({
    String? id,
    required this.sectionId,
    required this.name,
    required this.order,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.hasUnsavedChanges = false,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'section_id': sectionId,
      'name': name,
      'order_index': order,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory SubSectionModel.fromMap(Map<String, dynamic> map) {
    return SubSectionModel(
      id: map['id'],
      sectionId: map['section_id'],
      name: map['name'],
      order: map['order_index'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  SubSectionModel copyWith({
    String? name,
    int? order,
    DateTime? updatedAt,
    bool? hasUnsavedChanges,
  }) {
    return SubSectionModel(
      id: id,
      sectionId: sectionId,
      name: name ?? this.name,
      order: order ?? this.order,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      hasUnsavedChanges: hasUnsavedChanges ?? this.hasUnsavedChanges,
    );
  }
}
