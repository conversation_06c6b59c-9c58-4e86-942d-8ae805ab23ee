import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../../providers/grid_data_provider.dart';
import '../../core/constants/app_constants.dart';

class GridViewWidget extends StatefulWidget {
  const GridViewWidget({super.key});

  @override
  State<GridViewWidget> createState() => _GridViewWidgetState();
}

class _GridViewWidgetState extends State<GridViewWidget> {
  PlutoGridStateManager? stateManager;

  @override
  Widget build(BuildContext context) {
    return Consumer<GridDataProvider>(
      builder: (context, gridProvider, child) {
        if (gridProvider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (gridProvider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error: ${gridProvider.error}',
                  style: Theme.of(context).textTheme.titleMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    gridProvider.clearError();
                    if (gridProvider.currentSubSectionId != null) {
                      gridProvider.loadSubSectionData(gridProvider.currentSubSectionId!);
                    }
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (gridProvider.columns.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.table_chart_outlined,
                  size: 64,
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'No columns defined',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Add columns to start entering data',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                      ),
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () {
                    // TODO: Show add column dialog
                  },
                  icon: const Icon(Icons.add),
                  label: const Text('Add Column'),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // Grid
            Expanded(
              child: PlutoGrid(
                columns: gridProvider.plutoColumns,
                rows: gridProvider.plutoRows,
                onLoaded: (PlutoGridOnLoadedEvent event) {
                  stateManager = event.stateManager;
                  gridProvider.setStateManager(stateManager!);

                  // Configure grid settings
                  stateManager!.setShowColumnFilter(true);
                  stateManager!.setRowHeight(AppConstants.defaultRowHeight);
                },
                onChanged: (PlutoGridOnChangedEvent event) {
                  // Handle cell value changes
                  final rowId = event.row.key?.value as String?;
                  if (rowId != null) {
                    final updatedData = <String, dynamic>{};
                    event.row.cells.forEach((key, cell) {
                      updatedData[key] = cell.value;
                    });
                    gridProvider.updateRow(rowId, updatedData);
                  }
                },
                onRowDoubleTap: (PlutoGridOnRowDoubleTapEvent event) {
                  // Handle row double tap for editing
                  stateManager?.setEditingCell(
                    event.cell,
                    event.rowIdx,
                  );
                },
                onRowSecondaryTap: (PlutoGridOnRowSecondaryTapEvent event) {
                  // Show context menu
                  _showRowContextMenu(context, event, gridProvider);
                },
                // Column menu handling removed due to API changes
                configuration: PlutoGridConfiguration(
                  style: PlutoGridStyleConfig(
                    gridBorderColor: Theme.of(context).dividerColor,
                    activatedBorderColor: Theme.of(context).colorScheme.primary,
                    rowColor: Theme.of(context).colorScheme.surface,
                    oddRowColor: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                    evenRowColor: Theme.of(context).colorScheme.surface,
                    columnTextStyle: Theme.of(context).textTheme.titleSmall!,
                    cellTextStyle: Theme.of(context).textTheme.bodyMedium!,
                  ),
                  columnSize: const PlutoGridColumnSizeConfig(
                    autoSizeMode: PlutoAutoSizeMode.scale,
                    resizeMode: PlutoResizeMode.pushAndPull,
                  ),
                  scrollbar: const PlutoGridScrollbarConfig(
                    isAlwaysShown: true,
                    scrollbarThickness: 8,
                  ),
                  enterKeyAction: PlutoGridEnterKeyAction.editingAndMoveDown,
                  tabKeyAction: PlutoGridTabKeyAction.moveToNextOnEdge,
                ),
              ),
            ),

            // Pagination controls
            if (gridProvider.totalPages > 1)
              Container(
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(
                      color: Theme.of(context).dividerColor,
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Showing ${gridProvider.currentPage * AppConstants.defaultPageSize + 1}-'
                      '${(gridProvider.currentPage + 1) * AppConstants.defaultPageSize > gridProvider.totalRows ? gridProvider.totalRows : (gridProvider.currentPage + 1) * AppConstants.defaultPageSize} '
                      'of ${gridProvider.totalRows} rows',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    Row(
                      children: [
                        IconButton(
                          onPressed: gridProvider.currentPage > 0
                              ? () => gridProvider.previousPage()
                              : null,
                          icon: const Icon(Icons.chevron_left),
                          tooltip: 'Previous Page',
                        ),
                        Text(
                          'Page ${gridProvider.currentPage + 1} of ${gridProvider.totalPages}',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        IconButton(
                          onPressed: gridProvider.currentPage < gridProvider.totalPages - 1
                              ? () => gridProvider.nextPage()
                              : null,
                          icon: const Icon(Icons.chevron_right),
                          tooltip: 'Next Page',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
          ],
        );
      },
    );
  }

  void _showRowContextMenu(
    BuildContext context,
    PlutoGridOnRowSecondaryTapEvent event,
    GridDataProvider gridProvider,
  ) {
    final RenderBox overlay = Overlay.of(context).context.findRenderObject() as RenderBox;

    showMenu(
      context: context,
      position: RelativeRect.fromRect(
        event.offset & const Size(40, 40),
        Offset.zero & overlay.size,
      ),
      items: <PopupMenuEntry<String>>[
        PopupMenuItem<String>(
          value: 'insert_above',
          child: const Row(
            children: [
              Icon(Icons.add_circle_outline),
              SizedBox(width: 8),
              Text('Insert Row Above'),
            ],
          ),
          onTap: () {
            gridProvider.addRow();
          },
        ),
        PopupMenuItem<String>(
          value: 'insert_below',
          child: const Row(
            children: [
              Icon(Icons.add_circle_outline),
              SizedBox(width: 8),
              Text('Insert Row Below'),
            ],
          ),
          onTap: () {
            gridProvider.addRow();
          },
        ),
        const PopupMenuDivider(),
        PopupMenuItem<String>(
          value: 'duplicate',
          child: const Row(
            children: [
              Icon(Icons.copy),
              SizedBox(width: 8),
              Text('Duplicate Row'),
            ],
          ),
          onTap: () {
            // Duplicate functionality can be added later
          },
        ),
        const PopupMenuDivider(),
        PopupMenuItem<String>(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, color: Theme.of(context).colorScheme.error),
              const SizedBox(width: 8),
              Text(
                'Delete Row',
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
            ],
          ),
          onTap: () {
            final rowId = (event.row.key as ValueKey<String>).value;
            _showDeleteRowDialog(context, rowId, gridProvider);
          },
        ),
      ],
    );
  }

  // Column context menu removed due to API changes in PlutoGrid

  void _showDeleteRowDialog(
    BuildContext context,
    String rowId,
    GridDataProvider gridProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Row'),
        content: const Text('Are you sure you want to delete this row?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              gridProvider.deleteRow(rowId);
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
