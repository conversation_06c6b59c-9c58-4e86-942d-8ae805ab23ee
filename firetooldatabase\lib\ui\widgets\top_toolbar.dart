import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/section_provider.dart';
import '../../providers/grid_data_provider.dart';
import '../../core/services/excel_service.dart';
import '../../core/models/grid_data_model.dart';
import '../../core/constants/app_constants.dart';

class TopToolbar extends StatelessWidget {
  const TopToolbar({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<SectionProvider, GridDataProvider>(
      builder: (context, sectionProvider, gridProvider, child) {
        final hasSubSection = sectionProvider.currentSubSection != null;
        
        return Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
            vertical: AppConstants.smallPadding,
          ),
          child: Row(
            children: [
              // Breadcrumb navigation
              Expanded(
                child: _buildBreadcrumb(context, sectionProvider),
              ),
              
              if (hasSubSection) ...[
                // Search field
                SizedBox(
                  width: 250,
                  child: TextField(
                    decoration: const InputDecoration(
                      hintText: 'Search data...',
                      prefixIcon: Icon(Icons.search),
                      isDense: true,
                    ),
                    onChanged: (value) {
                      gridProvider.search(value);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                
                // Import button
                ElevatedButton.icon(
                  onPressed: () => _showImportDialog(context),
                  icon: const Icon(Icons.file_upload),
                  label: const Text('Import'),
                ),
                const SizedBox(width: 8),
                
                // Export button
                ElevatedButton.icon(
                  onPressed: () => _exportData(context, gridProvider),
                  icon: const Icon(Icons.file_download),
                  label: const Text('Export'),
                ),
                const SizedBox(width: 8),
                
                // Add column button
                IconButton(
                  onPressed: () => _showAddColumnDialog(context),
                  icon: const Icon(Icons.view_column),
                  tooltip: 'Add Column',
                ),
                
                // Add row button
                IconButton(
                  onPressed: () => gridProvider.addRow(),
                  icon: const Icon(Icons.add),
                  tooltip: 'Add Row',
                ),
                
                // More options
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert),
                  onSelected: (value) {
                    switch (value) {
                      case 'clear_data':
                        _showClearDataDialog(context);
                        break;
                      case 'column_settings':
                        _showColumnSettingsDialog(context);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'column_settings',
                      child: Text('Column Settings'),
                    ),
                    const PopupMenuItem(
                      value: 'clear_data',
                      child: Text('Clear All Data'),
                    ),
                  ],
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildBreadcrumb(BuildContext context, SectionProvider sectionProvider) {
    final items = <Widget>[];
    
    if (sectionProvider.currentSection != null) {
      items.add(
        Text(
          sectionProvider.currentSection!.name,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
      );
      
      if (sectionProvider.currentSubSection != null) {
        items.add(
          Icon(
            Icons.chevron_right,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
          ),
        );
        items.add(
          Text(
            sectionProvider.currentSubSection!.name,
            style: Theme.of(context).textTheme.titleMedium,
          ),
        );
      }
    } else {
      items.add(
        Text(
          'Select a section to get started',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
        ),
      );
    }
    
    return Row(children: items);
  }

  void _showImportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Data'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choose import method:'),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.file_upload),
              title: const Text('Import from Excel/CSV'),
              subtitle: const Text('Upload Excel or CSV files'),
              onTap: () {
                Navigator.of(context).pop();
                _importFromExcel(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.content_paste),
              title: const Text('Paste from Clipboard'),
              subtitle: const Text('Paste data from Excel or other sources'),
              onTap: () {
                Navigator.of(context).pop();
                _showPasteDialog(context);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Future<void> _importFromExcel(BuildContext context) async {
    try {
      final excelService = ExcelService();
      final result = await excelService.pickExcelFile();
      
      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.path != null) {
          final sheetsData = await excelService.parseExcelFile(file.path!);
          
          if (sheetsData.isNotEmpty) {
            _showSheetSelectionDialog(context, sheetsData);
          }
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error importing file: $e')),
      );
    }
  }

  void _showSheetSelectionDialog(BuildContext context, List<ExcelSheetData> sheetsData) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Sheet to Import'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: sheetsData.length,
            itemBuilder: (context, index) {
              final sheet = sheetsData[index];
              return ListTile(
                title: Text(sheet.sheetName),
                subtitle: Text('${sheet.rows.length} rows, ${sheet.headers.length} columns'),
                onTap: () {
                  Navigator.of(context).pop();
                  _importSheetData(context, sheet);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Future<void> _importSheetData(BuildContext context, ExcelSheetData sheetData) async {
    final gridProvider = context.read<GridDataProvider>();
    final sectionProvider = context.read<SectionProvider>();
    
    if (sectionProvider.currentSubSection == null) return;
    
    try {
      // First, create columns based on headers
      for (int i = 0; i < sheetData.headers.length; i++) {
        final header = sheetData.headers[i];
        final dataType = i < sheetData.columnTypes.length 
            ? sheetData.columnTypes[i] 
            : DataType.text;
        
        await gridProvider.addColumn(header, dataType);
      }
      
      // Then, import rows
      final rowsData = sheetData.rows.map((row) {
        final data = <String, dynamic>{};
        for (int i = 0; i < row.length && i < gridProvider.columns.length; i++) {
          final column = gridProvider.columns[i];
          data[column.id] = row[i];
        }
        return data;
      }).toList();
      
      await gridProvider.bulkInsertRows(rowsData);
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Imported ${rowsData.length} rows successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error importing data: $e')),
      );
    }
  }

  void _showPasteDialog(BuildContext context) {
    final textController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Paste Data'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: TextField(
            controller: textController,
            maxLines: null,
            expands: true,
            decoration: const InputDecoration(
              hintText: 'Paste your data here (tab or comma separated)',
              border: OutlineInputBorder(),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _processPastedData(context, textController.text);
            },
            child: const Text('Import'),
          ),
        ],
      ),
    );
  }

  void _processPastedData(BuildContext context, String data) {
    // TODO: Implement paste data processing
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Paste import feature coming soon')),
    );
  }

  Future<void> _exportData(BuildContext context, GridDataProvider gridProvider) async {
    try {
      final excelService = ExcelService();
      final sectionProvider = context.read<SectionProvider>();
      
      if (sectionProvider.currentSubSection == null) return;
      
      final fileName = '${sectionProvider.currentSection?.name}_${sectionProvider.currentSubSection?.name}';
      
      final filePath = await excelService.exportToExcel(
        gridProvider.columns,
        gridProvider.rows,
        fileName,
      );
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Exported to: $filePath')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error exporting data: $e')),
      );
    }
  }

  void _showAddColumnDialog(BuildContext context) {
    final nameController = TextEditingController();
    DataType selectedType = DataType.text;
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Add Column'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Column Name',
                  hintText: 'Enter column name',
                ),
                autofocus: true,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<DataType>(
                value: selectedType,
                decoration: const InputDecoration(
                  labelText: 'Data Type',
                ),
                items: DataType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type.name.toUpperCase()),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      selectedType = value;
                    });
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (nameController.text.trim().isNotEmpty) {
                  context.read<GridDataProvider>().addColumn(
                        nameController.text.trim(),
                        selectedType,
                      );
                  Navigator.of(context).pop();
                }
              },
              child: const Text('Add'),
            ),
          ],
        ),
      ),
    );
  }

  void _showClearDataDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text('Are you sure you want to clear all data in this sub-section? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Implement clear all data
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Clear data feature coming soon')),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _showColumnSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Column Settings'),
        content: const Text('Column settings feature coming soon'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
