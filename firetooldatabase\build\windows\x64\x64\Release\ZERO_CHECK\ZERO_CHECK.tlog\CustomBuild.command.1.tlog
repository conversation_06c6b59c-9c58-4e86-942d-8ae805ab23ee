^D:\FIRETOOLDATABASE\FIRETOOLDATABASE\BUILD\WINDOWS\X64\CMAKEFILES\41425BDC89F16EE5515BC3D14E267397\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/Firetooldatabase/firetooldatabase/windows -BD:/Firetooldatabase/firetooldatabase/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/Firetooldatabase/firetooldatabase/build/windows/x64/firetooldatabase.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
