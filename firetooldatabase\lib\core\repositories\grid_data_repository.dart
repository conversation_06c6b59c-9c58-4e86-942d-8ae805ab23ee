import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/grid_data_model.dart';

class GridDataRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Column operations
  Future<List<ColumnModel>> getColumnsBySubSectionId(String subSectionId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'columns',
      where: 'sub_section_id = ?',
      whereArgs: [subSectionId],
      orderBy: 'order_index ASC',
    );

    return List.generate(maps.length, (i) {
      return ColumnModel.fromMap(maps[i]);
    });
  }

  Future<ColumnModel?> getColumnById(String id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'columns',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return ColumnModel.fromMap(maps.first);
    }
    return null;
  }

  Future<String> insertColumn(ColumnModel column) async {
    final db = await _databaseHelper.database;
    await db.insert(
      'columns',
      column.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    return column.id;
  }

  Future<void> updateColumn(ColumnModel column) async {
    final db = await _databaseHelper.database;
    await db.update(
      'columns',
      column.toMap(),
      where: 'id = ?',
      whereArgs: [column.id],
    );
  }

  Future<void> deleteColumn(String id) async {
    final db = await _databaseHelper.database;
    await db.delete(
      'columns',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> reorderColumns(String subSectionId, List<String> columnIds) async {
    final db = await _databaseHelper.database;
    await db.transaction((txn) async {
      for (int i = 0; i < columnIds.length; i++) {
        await txn.update(
          'columns',
          {'order_index': i, 'updated_at': DateTime.now().toIso8601String()},
          where: 'id = ? AND sub_section_id = ?',
          whereArgs: [columnIds[i], subSectionId],
        );
      }
    });
  }

  Future<int> getNextColumnOrder(String subSectionId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT MAX(order_index) as max_order FROM columns WHERE sub_section_id = ?',
      [subSectionId],
    );
    final maxOrder = result.first['max_order'] as int?;
    return (maxOrder ?? -1) + 1;
  }

  // Row operations
  Future<List<RowModel>> getRowsBySubSectionId(
    String subSectionId, {
    int? limit,
    int? offset,
    String? orderBy,
    bool ascending = true,
  }) async {
    final db = await _databaseHelper.database;
    
    String query = 'SELECT * FROM rows WHERE sub_section_id = ? AND is_deleted = 0';
    List<dynamic> args = [subSectionId];
    
    if (orderBy != null) {
      query += ' ORDER BY $orderBy ${ascending ? 'ASC' : 'DESC'}';
    } else {
      query += ' ORDER BY created_at DESC';
    }
    
    if (limit != null) {
      query += ' LIMIT $limit';
      if (offset != null) {
        query += ' OFFSET $offset';
      }
    }

    final List<Map<String, dynamic>> maps = await db.rawQuery(query, args);

    return List.generate(maps.length, (i) {
      return RowModel.fromMap(maps[i]);
    });
  }

  Future<RowModel?> getRowById(String id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'rows',
      where: 'id = ? AND is_deleted = 0',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return RowModel.fromMap(maps.first);
    }
    return null;
  }

  Future<String> insertRow(RowModel row) async {
    final db = await _databaseHelper.database;
    await db.insert(
      'rows',
      row.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    return row.id;
  }

  Future<void> updateRow(RowModel row) async {
    final db = await _databaseHelper.database;
    await db.update(
      'rows',
      row.toMap(),
      where: 'id = ?',
      whereArgs: [row.id],
    );
  }

  Future<void> deleteRow(String id) async {
    final db = await _databaseHelper.database;
    await db.update(
      'rows',
      {
        'is_deleted': 1,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> permanentlyDeleteRow(String id) async {
    final db = await _databaseHelper.database;
    await db.delete(
      'rows',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<int> getRowCount(String subSectionId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM rows WHERE sub_section_id = ? AND is_deleted = 0',
      [subSectionId],
    );
    return result.first['count'] as int;
  }

  Future<List<RowModel>> searchRows(
    String subSectionId,
    String searchTerm, {
    int? limit,
    int? offset,
  }) async {
    final db = await _databaseHelper.database;
    
    String query = '''
      SELECT * FROM rows 
      WHERE sub_section_id = ? AND is_deleted = 0 
      AND data LIKE ?
      ORDER BY updated_at DESC
    ''';
    
    List<dynamic> args = [subSectionId, '%$searchTerm%'];
    
    if (limit != null) {
      query += ' LIMIT $limit';
      if (offset != null) {
        query += ' OFFSET $offset';
      }
    }

    final List<Map<String, dynamic>> maps = await db.rawQuery(query, args);

    return List.generate(maps.length, (i) {
      return RowModel.fromMap(maps[i]);
    });
  }

  Future<void> bulkInsertRows(List<RowModel> rows) async {
    final db = await _databaseHelper.database;
    await db.transaction((txn) async {
      for (final row in rows) {
        await txn.insert(
          'rows',
          row.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    });
  }

  Future<void> bulkUpdateRows(List<RowModel> rows) async {
    final db = await _databaseHelper.database;
    await db.transaction((txn) async {
      for (final row in rows) {
        await txn.update(
          'rows',
          row.toMap(),
          where: 'id = ?',
          whereArgs: [row.id],
        );
      }
    });
  }

  Future<List<RowModel>> getUnsyncedRows() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'rows',
      where: 'supabase_id IS NULL AND is_deleted = 0',
      orderBy: 'created_at ASC',
    );

    return List.generate(maps.length, (i) {
      return RowModel.fromMap(maps[i]);
    });
  }

  Future<void> markRowAsSynced(String rowId, String supabaseId) async {
    final db = await _databaseHelper.database;
    await db.update(
      'rows',
      {
        'supabase_id': supabaseId,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [rowId],
    );
  }
}
