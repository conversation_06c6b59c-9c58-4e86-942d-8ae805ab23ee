^D:\FIRETOOLDATABASE\FIRETOOLDATABASE\BUILD\WINDOWS\X64\CMAKEFILES\9F22C17FF2432541AD30FA9414FFFBD7\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\flutter PROJECT_DIR=D:\Firetooldatabase\firetooldatabase FLUTTER_ROOT=C:\flutter FLUTTER_EPHEMERAL_DIR=D:\Firetooldatabase\firetooldatabase\windows\flutter\ephemeral PROJECT_DIR=D:\Firetooldatabase\firetooldatabase FLUTTER_TARGET=lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=true PACKAGE_CONFIG=D:\Firetooldatabase\firetooldatabase\.dart_tool\package_config.json C:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\FIRETOOLDATABASE\FIRETOOLDATABASE\BUILD\WINDOWS\X64\CMAKEFILES\5E0E2F8B962AC2A87BA2F7B6CB04CF65\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\FIRETOOLDATABASE\FIRETOOLDATABASE\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/Firetooldatabase/firetooldatabase/windows -BD:/Firetooldatabase/firetooldatabase/build/windows/x64 --check-stamp-file D:/Firetooldatabase/firetooldatabase/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
