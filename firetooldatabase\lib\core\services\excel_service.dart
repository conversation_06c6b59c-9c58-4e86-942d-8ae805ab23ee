import 'dart:io';
import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import '../models/grid_data_model.dart';
import '../models/section_model.dart';

class ExcelService {
  Future<FilePickerResult?> pickExcelFile() async {
    return await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['xlsx', 'xls', 'csv'],
      allowMultiple: false,
    );
  }

  Future<List<ExcelSheetData>> parseExcelFile(String filePath) async {
    final file = File(filePath);
    final bytes = await file.readAsBytes();
    final excel = Excel.decodeBytes(bytes);
    
    List<ExcelSheetData> sheetsData = [];
    
    for (String sheetName in excel.tables.keys) {
      final sheet = excel.tables[sheetName];
      if (sheet == null) continue;
      
      List<List<String>> rows = [];
      List<String> headers = [];
      List<DataType> columnTypes = [];
      
      // Process rows
      for (int rowIndex = 0; rowIndex < sheet.maxRows; rowIndex++) {
        List<String> rowData = [];
        
        for (int colIndex = 0; colIndex < sheet.maxColumns; colIndex++) {
          final cell = sheet.cell(CellIndex.indexByColumnRow(
            columnIndex: colIndex,
            rowIndex: rowIndex,
          ));
          
          String cellValue = '';
          if (cell.value != null) {
            cellValue = cell.value.toString();
          }
          rowData.add(cellValue);
        }
        
        if (rowIndex == 0) {
          // First row as headers
          headers = rowData;
          columnTypes = _detectColumnTypes(sheet, headers.length);
        } else {
          // Skip empty rows
          if (rowData.any((cell) => cell.isNotEmpty)) {
            rows.add(rowData);
          }
        }
      }
      
      if (headers.isNotEmpty) {
        sheetsData.add(ExcelSheetData(
          sheetName: sheetName,
          headers: headers,
          rows: rows,
          columnTypes: columnTypes,
        ));
      }
    }
    
    return sheetsData;
  }

  List<DataType> _detectColumnTypes(Sheet sheet, int columnCount) {
    List<DataType> types = [];
    
    for (int colIndex = 0; colIndex < columnCount; colIndex++) {
      DataType detectedType = DataType.text;
      int sampleSize = 0;
      int numberCount = 0;
      int dateCount = 0;
      int booleanCount = 0;
      int emailCount = 0;
      int phoneCount = 0;
      int urlCount = 0;
      
      // Sample first 10 non-empty cells to detect type
      for (int rowIndex = 1; rowIndex < sheet.maxRows && sampleSize < 10; rowIndex++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(
          columnIndex: colIndex,
          rowIndex: rowIndex,
        ));
        
        if (cell.value != null && cell.value.toString().isNotEmpty) {
          String value = cell.value.toString().trim();
          sampleSize++;
          
          // Check for different data types
          if (_isNumber(value)) numberCount++;
          if (_isDate(value)) dateCount++;
          if (_isBoolean(value)) booleanCount++;
          if (_isEmail(value)) emailCount++;
          if (_isPhone(value)) phoneCount++;
          if (_isUrl(value)) urlCount++;
        }
      }
      
      if (sampleSize > 0) {
        double threshold = sampleSize * 0.7; // 70% threshold
        
        if (booleanCount >= threshold) {
          detectedType = DataType.boolean;
        } else if (dateCount >= threshold) {
          detectedType = DataType.date;
        } else if (numberCount >= threshold) {
          detectedType = DataType.number;
        } else if (emailCount >= threshold) {
          detectedType = DataType.email;
        } else if (phoneCount >= threshold) {
          detectedType = DataType.phone;
        } else if (urlCount >= threshold) {
          detectedType = DataType.url;
        }
      }
      
      types.add(detectedType);
    }
    
    return types;
  }

  bool _isNumber(String value) {
    return double.tryParse(value.replaceAll(',', '')) != null;
  }

  bool _isDate(String value) {
    try {
      DateTime.parse(value);
      return true;
    } catch (e) {
      // Try common date formats
      final dateRegex = RegExp(r'^\d{1,2}[/-]\d{1,2}[/-]\d{2,4}$');
      return dateRegex.hasMatch(value);
    }
  }

  bool _isBoolean(String value) {
    final lowerValue = value.toLowerCase();
    return ['true', 'false', 'yes', 'no', '1', '0', 'y', 'n'].contains(lowerValue);
  }

  bool _isEmail(String value) {
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    return emailRegex.hasMatch(value);
  }

  bool _isPhone(String value) {
    final phoneRegex = RegExp(r'^[\+]?[1-9][\d]{0,15}$');
    final cleanValue = value.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    return phoneRegex.hasMatch(cleanValue);
  }

  bool _isUrl(String value) {
    final urlRegex = RegExp(r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$');
    return urlRegex.hasMatch(value);
  }

  Future<String> exportToExcel(
    List<ColumnModel> columns,
    List<RowModel> rows,
    String fileName,
  ) async {
    final excel = Excel.createExcel();
    final sheet = excel['Sheet1'];
    
    // Add headers
    for (int i = 0; i < columns.length; i++) {
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = TextCellValue(columns[i].name);
    }
    
    // Add data rows
    for (int rowIndex = 0; rowIndex < rows.length; rowIndex++) {
      final rowData = rows[rowIndex];
      for (int colIndex = 0; colIndex < columns.length; colIndex++) {
        final columnId = columns[colIndex].id;
        final cellValue = rowData.data[columnId]?.toString() ?? '';
        
        sheet.cell(CellIndex.indexByColumnRow(
          columnIndex: colIndex,
          rowIndex: rowIndex + 1,
        )).value = TextCellValue(cellValue);
      }
    }
    
    // Save file
    final directory = await getApplicationDocumentsDirectory();
    final filePath = '${directory.path}/$fileName.xlsx';
    final file = File(filePath);
    await file.writeAsBytes(excel.encode()!);
    
    return filePath;
  }

  Future<String?> selectExportLocation() async {
    return await FilePicker.platform.saveFile(
      dialogTitle: 'Save Excel File',
      fileName: 'export_${DateTime.now().millisecondsSinceEpoch}.xlsx',
      type: FileType.custom,
      allowedExtensions: ['xlsx'],
    );
  }
}

class ExcelSheetData {
  final String sheetName;
  final List<String> headers;
  final List<List<String>> rows;
  final List<DataType> columnTypes;

  ExcelSheetData({
    required this.sheetName,
    required this.headers,
    required this.rows,
    required this.columnTypes,
  });
}
