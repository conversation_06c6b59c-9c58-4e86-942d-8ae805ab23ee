import 'package:flutter/foundation.dart';
import '../core/models/section_model.dart';
import '../core/repositories/section_repository.dart';

class SectionProvider with ChangeNotifier {
  final SectionRepository _sectionRepository = SectionRepository();
  
  List<SectionModel> _sections = [];
  SectionModel? _currentSection;
  SubSectionModel? _currentSubSection;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<SectionModel> get sections => _sections;
  SectionModel? get currentSection => _currentSection;
  SubSectionModel? get currentSubSection => _currentSubSection;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Load all sections
  Future<void> loadSections() async {
    _setLoading(true);
    try {
      _sections = await _sectionRepository.getAllSections();
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Set current section
  void setCurrentSection(SectionModel? section) {
    _currentSection = section;
    _currentSubSection = null; // Reset sub-section when section changes
    notifyListeners();
  }

  // Set current sub-section
  void setCurrentSubSection(SubSectionModel? subSection) {
    _currentSubSection = subSection;
    notifyListeners();
  }

  // Create new section
  Future<void> createSection(String name, {String? iconName}) async {
    _setLoading(true);
    try {
      final order = await _sectionRepository.getNextSectionOrder();
      final newSection = SectionModel(
        name: name,
        iconName: iconName,
        order: order,
      );
      
      await _sectionRepository.insertSection(newSection);
      await loadSections(); // Reload to get updated list
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Update section
  Future<void> updateSection(SectionModel section) async {
    _setLoading(true);
    try {
      await _sectionRepository.updateSection(section);
      await loadSections(); // Reload to get updated list
      
      // Update current section if it's the one being updated
      if (_currentSection?.id == section.id) {
        _currentSection = section;
      }
      
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Delete section
  Future<void> deleteSection(String sectionId) async {
    _setLoading(true);
    try {
      await _sectionRepository.deleteSection(sectionId);
      await loadSections(); // Reload to get updated list
      
      // Clear current section if it's the one being deleted
      if (_currentSection?.id == sectionId) {
        _currentSection = null;
        _currentSubSection = null;
      }
      
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Reorder sections
  Future<void> reorderSections(List<String> sectionIds) async {
    try {
      await _sectionRepository.reorderSections(sectionIds);
      await loadSections(); // Reload to get updated order
      _error = null;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  // Create new sub-section
  Future<void> createSubSection(String sectionId, String name) async {
    _setLoading(true);
    try {
      final order = await _sectionRepository.getNextSubSectionOrder(sectionId);
      final newSubSection = SubSectionModel(
        sectionId: sectionId,
        name: name,
        order: order,
      );
      
      await _sectionRepository.insertSubSection(newSubSection);
      await loadSections(); // Reload to get updated list
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Update sub-section
  Future<void> updateSubSection(SubSectionModel subSection) async {
    _setLoading(true);
    try {
      await _sectionRepository.updateSubSection(subSection);
      await loadSections(); // Reload to get updated list
      
      // Update current sub-section if it's the one being updated
      if (_currentSubSection?.id == subSection.id) {
        _currentSubSection = subSection;
      }
      
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Delete sub-section
  Future<void> deleteSubSection(String subSectionId) async {
    _setLoading(true);
    try {
      await _sectionRepository.deleteSubSection(subSectionId);
      await loadSections(); // Reload to get updated list
      
      // Clear current sub-section if it's the one being deleted
      if (_currentSubSection?.id == subSectionId) {
        _currentSubSection = null;
      }
      
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Reorder sub-sections
  Future<void> reorderSubSections(String sectionId, List<String> subSectionIds) async {
    try {
      await _sectionRepository.reorderSubSections(sectionId, subSectionIds);
      await loadSections(); // Reload to get updated order
      _error = null;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  // Get sub-sections for a specific section
  List<SubSectionModel> getSubSections(String sectionId) {
    final section = _sections.firstWhere(
      (s) => s.id == sectionId,
      orElse: () => SectionModel(name: '', order: 0),
    );
    return section.subSections;
  }

  // Mark sub-section as having unsaved changes
  void markSubSectionAsChanged(String subSectionId, bool hasChanges) {
    if (_currentSubSection?.id == subSectionId) {
      _currentSubSection = _currentSubSection!.copyWith(
        hasUnsavedChanges: hasChanges,
      );
      notifyListeners();
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Get section by ID
  SectionModel? getSectionById(String id) {
    try {
      return _sections.firstWhere((s) => s.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get sub-section by ID
  SubSectionModel? getSubSectionById(String id) {
    for (final section in _sections) {
      try {
        return section.subSections.firstWhere((s) => s.id == id);
      } catch (e) {
        continue;
      }
    }
    return null;
  }
}
