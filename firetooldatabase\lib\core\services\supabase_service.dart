import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/section_model.dart';
import '../models/grid_data_model.dart';
import '../constants/app_constants.dart';

class SupabaseService {
  static final SupabaseService _instance = SupabaseService._internal();
  factory SupabaseService() => _instance;
  SupabaseService._internal();

  SupabaseClient get client => Supabase.instance.client;

  Future<void> initialize(String url, String anonKey) async {
    await Supabase.initialize(
      url: url,
      anonKey: anon<PERSON><PERSON>,
    );
  }

  // Section sync operations
  Future<List<SectionModel>> uploadSections(List<SectionModel> sections) async {
    try {
      final List<Map<String, dynamic>> sectionsData = 
          sections.map((s) => s.toMap()).toList();
      
      final response = await client
          .from('sections')
          .upsert(sectionsData)
          .select();
      
      return response.map((data) => SectionModel.fromMap(data)).toList();
    } catch (e) {
      throw Exception('Failed to upload sections: $e');
    }
  }

  Future<List<SectionModel>> downloadSections() async {
    try {
      final response = await client
          .from('sections')
          .select()
          .order('order_index');
      
      return response.map((data) => SectionModel.fromMap(data)).toList();
    } catch (e) {
      throw Exception('Failed to download sections: $e');
    }
  }

  Future<List<SubSectionModel>> uploadSubSections(List<SubSectionModel> subSections) async {
    try {
      final List<Map<String, dynamic>> subSectionsData = 
          subSections.map((s) => s.toMap()).toList();
      
      final response = await client
          .from('sub_sections')
          .upsert(subSectionsData)
          .select();
      
      return response.map((data) => SubSectionModel.fromMap(data)).toList();
    } catch (e) {
      throw Exception('Failed to upload sub-sections: $e');
    }
  }

  Future<List<SubSectionModel>> downloadSubSections() async {
    try {
      final response = await client
          .from('sub_sections')
          .select()
          .order('section_id, order_index');
      
      return response.map((data) => SubSectionModel.fromMap(data)).toList();
    } catch (e) {
      throw Exception('Failed to download sub-sections: $e');
    }
  }

  // Column sync operations
  Future<List<ColumnModel>> uploadColumns(List<ColumnModel> columns) async {
    try {
      final List<Map<String, dynamic>> columnsData = 
          columns.map((c) => c.toMap()).toList();
      
      final response = await client
          .from('columns')
          .upsert(columnsData)
          .select();
      
      return response.map((data) => ColumnModel.fromMap(data)).toList();
    } catch (e) {
      throw Exception('Failed to upload columns: $e');
    }
  }

  Future<List<ColumnModel>> downloadColumns() async {
    try {
      final response = await client
          .from('columns')
          .select()
          .order('sub_section_id, order_index');
      
      return response.map((data) => ColumnModel.fromMap(data)).toList();
    } catch (e) {
      throw Exception('Failed to download columns: $e');
    }
  }

  // Row sync operations
  Future<List<RowModel>> uploadRows(List<RowModel> rows) async {
    try {
      final List<Map<String, dynamic>> rowsData = 
          rows.map((r) => r.toMap()).toList();
      
      final response = await client
          .from('rows')
          .upsert(rowsData)
          .select();
      
      return response.map((data) => RowModel.fromMap(data)).toList();
    } catch (e) {
      throw Exception('Failed to upload rows: $e');
    }
  }

  Future<List<RowModel>> downloadRows({String? subSectionId}) async {
    try {
      var query = client.from('rows').select();
      
      if (subSectionId != null) {
        query = query.eq('sub_section_id', subSectionId);
      }
      
      final response = await query.order('created_at');
      
      return response.map((data) => RowModel.fromMap(data)).toList();
    } catch (e) {
      throw Exception('Failed to download rows: $e');
    }
  }

  // Batch upload with progress tracking
  Future<void> batchUploadRows(
    List<RowModel> rows,
    Function(int uploaded, int total)? onProgress,
  ) async {
    try {
      final int batchSize = AppConstants.syncBatchSize;
      final int totalBatches = (rows.length / batchSize).ceil();
      
      for (int i = 0; i < totalBatches; i++) {
        final int start = i * batchSize;
        final int end = (start + batchSize > rows.length) 
            ? rows.length 
            : start + batchSize;
        
        final batch = rows.sublist(start, end);
        await uploadRows(batch);
        
        onProgress?.call(end, rows.length);
      }
    } catch (e) {
      throw Exception('Failed to batch upload rows: $e');
    }
  }

  // Conflict resolution
  Future<List<ConflictData>> detectConflicts(List<RowModel> localRows) async {
    try {
      List<ConflictData> conflicts = [];
      
      for (final localRow in localRows) {
        if (localRow.supabaseId != null) {
          final response = await client
              .from('rows')
              .select()
              .eq('id', localRow.supabaseId!)
              .single();
          
          final remoteRow = RowModel.fromMap(response);
          
          if (remoteRow.updatedAt.isAfter(localRow.updatedAt)) {
            conflicts.add(ConflictData(
              localRow: localRow,
              remoteRow: remoteRow,
              conflictType: ConflictType.updateConflict,
            ));
          }
        }
      }
      
      return conflicts;
    } catch (e) {
      throw Exception('Failed to detect conflicts: $e');
    }
  }

  Future<void> resolveConflict(
    ConflictData conflict,
    ConflictResolution resolution,
  ) async {
    try {
      switch (resolution) {
        case ConflictResolution.keepLocal:
          await uploadRows([conflict.localRow]);
          break;
        case ConflictResolution.keepRemote:
          // Remote data will be downloaded in next sync
          break;
        case ConflictResolution.merge:
          // Implement merge logic based on your requirements
          final mergedRow = _mergeRows(conflict.localRow, conflict.remoteRow);
          await uploadRows([mergedRow]);
          break;
      }
    } catch (e) {
      throw Exception('Failed to resolve conflict: $e');
    }
  }

  RowModel _mergeRows(RowModel localRow, RowModel remoteRow) {
    // Simple merge strategy - prefer local data for user-modified fields
    // and remote data for system fields
    final mergedData = Map<String, dynamic>.from(remoteRow.data);
    
    // Override with local changes (you can customize this logic)
    localRow.data.forEach((key, value) {
      if (value != null && value.toString().isNotEmpty) {
        mergedData[key] = value;
      }
    });
    
    return localRow.copyWith(
      data: mergedData,
      updatedAt: DateTime.now(),
    );
  }

  // Sync status tracking
  Future<SyncStatus> getSyncStatus() async {
    try {
      // Check connection and last sync time
      final response = await client
          .from('sync_status')
          .select()
          .limit(1);
      
      return SyncStatus(
        isOnline: true,
        lastSyncTime: DateTime.now(),
        pendingUploads: 0,
        pendingDownloads: 0,
      );
    } catch (e) {
      return SyncStatus(
        isOnline: false,
        lastSyncTime: null,
        pendingUploads: 0,
        pendingDownloads: 0,
      );
    }
  }

  // Real-time subscriptions
  RealtimeChannel subscribeToChanges(
    String table,
    Function(Map<String, dynamic>) onInsert,
    Function(Map<String, dynamic>) onUpdate,
    Function(Map<String, dynamic>) onDelete,
  ) {
    return client
        .channel('public:$table')
        .onPostgresChanges(
          event: PostgresChangeEvent.insert,
          schema: 'public',
          table: table,
          callback: onInsert,
        )
        .onPostgresChanges(
          event: PostgresChangeEvent.update,
          schema: 'public',
          table: table,
          callback: onUpdate,
        )
        .onPostgresChanges(
          event: PostgresChangeEvent.delete,
          schema: 'public',
          table: table,
          callback: onDelete,
        )
        .subscribe();
  }
}

class ConflictData {
  final RowModel localRow;
  final RowModel remoteRow;
  final ConflictType conflictType;

  ConflictData({
    required this.localRow,
    required this.remoteRow,
    required this.conflictType,
  });
}

enum ConflictType {
  updateConflict,
  deleteConflict,
  insertConflict,
}

enum ConflictResolution {
  keepLocal,
  keepRemote,
  merge,
}

class SyncStatus {
  final bool isOnline;
  final DateTime? lastSyncTime;
  final int pendingUploads;
  final int pendingDownloads;

  SyncStatus({
    required this.isOnline,
    this.lastSyncTime,
    required this.pendingUploads,
    required this.pendingDownloads,
  });
}
