^D:\FIRETOOLDATABASE\FIRETOOLDATABASE\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/Firetooldatabase/firetooldatabase/windows -BD:/Firetooldatabase/firetooldatabase/build/windows/x64 --check-stamp-file D:/Firetooldatabase/firetooldatabase/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
