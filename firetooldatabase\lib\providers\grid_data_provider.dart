import 'package:flutter/foundation.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../core/models/grid_data_model.dart';
import '../core/repositories/grid_data_repository.dart';
import '../core/constants/app_constants.dart';

class GridDataProvider with ChangeNotifier {
  final GridDataRepository _gridDataRepository = GridDataRepository();
  
  List<ColumnModel> _columns = [];
  List<RowModel> _rows = [];
  List<PlutoColumn> _plutoColumns = [];
  List<PlutoRow> _plutoRows = [];
  PlutoGridStateManager? _stateManager;
  
  bool _isLoading = false;
  String? _error;
  String? _currentSubSectionId;
  int _currentPage = 0;
  int _totalRows = 0;
  String _searchTerm = '';
  String? _sortColumn;
  bool _sortAscending = true;

  // Getters
  List<ColumnModel> get columns => _columns;
  List<RowModel> get rows => _rows;
  List<PlutoColumn> get plutoColumns => _plutoColumns;
  List<PlutoRow> get plutoRows => _plutoRows;
  PlutoGridStateManager? get stateManager => _stateManager;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get currentSubSectionId => _currentSubSectionId;
  int get currentPage => _currentPage;
  int get totalRows => _totalRows;
  int get totalPages => (_totalRows / AppConstants.defaultPageSize).ceil();
  String get searchTerm => _searchTerm;

  // Set state manager
  void setStateManager(PlutoGridStateManager stateManager) {
    _stateManager = stateManager;
  }

  // Load data for a sub-section
  Future<void> loadSubSectionData(String subSectionId) async {
    if (_currentSubSectionId == subSectionId && _columns.isNotEmpty) {
      return; // Already loaded
    }

    _setLoading(true);
    _currentSubSectionId = subSectionId;
    _currentPage = 0;
    _searchTerm = '';
    
    try {
      // Load columns
      _columns = await _gridDataRepository.getColumnsBySubSectionId(subSectionId);
      
      // Load rows with pagination
      await _loadRows();
      
      // Convert to Pluto format
      _convertToPlutoFormat();
      
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Load rows with pagination and search
  Future<void> _loadRows() async {
    if (_currentSubSectionId == null) return;

    if (_searchTerm.isEmpty) {
      _rows = await _gridDataRepository.getRowsBySubSectionId(
        _currentSubSectionId!,
        limit: AppConstants.defaultPageSize,
        offset: _currentPage * AppConstants.defaultPageSize,
        orderBy: _sortColumn,
        ascending: _sortAscending,
      );
      _totalRows = await _gridDataRepository.getRowCount(_currentSubSectionId!);
    } else {
      _rows = await _gridDataRepository.searchRows(
        _currentSubSectionId!,
        _searchTerm,
        limit: AppConstants.defaultPageSize,
        offset: _currentPage * AppConstants.defaultPageSize,
      );
      // For search, we don't have exact count, so estimate
      _totalRows = _rows.length < AppConstants.defaultPageSize 
          ? (_currentPage * AppConstants.defaultPageSize) + _rows.length
          : (_currentPage + 2) * AppConstants.defaultPageSize;
    }
  }

  // Convert data to Pluto Grid format
  void _convertToPlutoFormat() {
    // Convert columns
    _plutoColumns = _columns.map((column) {
      return PlutoColumn(
        title: column.name,
        field: column.id,
        type: _getPlutoColumnType(column.dataType),
        width: column.width,
        minWidth: 80,
        enableEditingMode: true,
        enableSorting: true,
        enableColumnDrag: true,
        enableContextMenu: true,
      );
    }).toList();

    // Convert rows
    _plutoRows = _rows.map((row) {
      Map<String, PlutoCell> cells = {};
      
      for (final column in _columns) {
        final value = row.data[column.id] ?? '';
        cells[column.id] = PlutoCell(value: value);
      }
      
      return PlutoRow(
        key: ValueKey(row.id),
        cells: cells,
      );
    }).toList();
  }

  PlutoColumnType _getPlutoColumnType(DataType dataType) {
    switch (dataType) {
      case DataType.number:
        return PlutoColumnType.number();
      case DataType.currency:
        return PlutoColumnType.currency();
      case DataType.date:
        return PlutoColumnType.date();
      case DataType.boolean:
        return PlutoColumnType.select(['true', 'false']);
      default:
        return PlutoColumnType.text();
    }
  }

  // Add new column
  Future<void> addColumn(String name, DataType dataType) async {
    if (_currentSubSectionId == null) return;

    _setLoading(true);
    try {
      final order = await _gridDataRepository.getNextColumnOrder(_currentSubSectionId!);
      final newColumn = ColumnModel(
        subSectionId: _currentSubSectionId!,
        name: name,
        dataType: dataType,
        order: order,
      );
      
      await _gridDataRepository.insertColumn(newColumn);
      await loadSubSectionData(_currentSubSectionId!); // Reload data
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Update column
  Future<void> updateColumn(ColumnModel column) async {
    _setLoading(true);
    try {
      await _gridDataRepository.updateColumn(column);
      await loadSubSectionData(_currentSubSectionId!); // Reload data
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Delete column
  Future<void> deleteColumn(String columnId) async {
    _setLoading(true);
    try {
      await _gridDataRepository.deleteColumn(columnId);
      await loadSubSectionData(_currentSubSectionId!); // Reload data
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Add new row
  Future<void> addRow([Map<String, dynamic>? initialData]) async {
    if (_currentSubSectionId == null) return;

    _setLoading(true);
    try {
      final data = initialData ?? {};
      
      // Set default values for columns
      for (final column in _columns) {
        if (!data.containsKey(column.id) && column.defaultValue != null) {
          data[column.id] = column.defaultValue;
        }
      }
      
      final newRow = RowModel(
        subSectionId: _currentSubSectionId!,
        data: data,
      );
      
      await _gridDataRepository.insertRow(newRow);
      await _loadRows(); // Reload current page
      _convertToPlutoFormat();
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Update row
  Future<void> updateRow(String rowId, Map<String, dynamic> data) async {
    try {
      final existingRow = _rows.firstWhere((r) => r.id == rowId);
      final updatedRow = existingRow.copyWith(data: data);
      
      await _gridDataRepository.updateRow(updatedRow);
      
      // Update local data
      final index = _rows.indexWhere((r) => r.id == rowId);
      if (index != -1) {
        _rows[index] = updatedRow;
        _convertToPlutoFormat();
        notifyListeners();
      }
      
      _error = null;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  // Delete row
  Future<void> deleteRow(String rowId) async {
    _setLoading(true);
    try {
      await _gridDataRepository.deleteRow(rowId);
      await _loadRows(); // Reload current page
      _convertToPlutoFormat();
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Search functionality
  Future<void> search(String searchTerm) async {
    _searchTerm = searchTerm;
    _currentPage = 0;
    await _loadRows();
    _convertToPlutoFormat();
    notifyListeners();
  }

  // Pagination
  Future<void> goToPage(int page) async {
    if (page < 0 || page >= totalPages) return;
    
    _currentPage = page;
    await _loadRows();
    _convertToPlutoFormat();
    notifyListeners();
  }

  Future<void> nextPage() async {
    await goToPage(_currentPage + 1);
  }

  Future<void> previousPage() async {
    await goToPage(_currentPage - 1);
  }

  // Sorting
  Future<void> sortBy(String columnId, bool ascending) async {
    _sortColumn = columnId;
    _sortAscending = ascending;
    _currentPage = 0;
    await _loadRows();
    _convertToPlutoFormat();
    notifyListeners();
  }

  // Bulk operations
  Future<void> bulkInsertRows(List<Map<String, dynamic>> rowsData) async {
    if (_currentSubSectionId == null) return;

    _setLoading(true);
    try {
      final rows = rowsData.map((data) => RowModel(
        subSectionId: _currentSubSectionId!,
        data: data,
      )).toList();
      
      await _gridDataRepository.bulkInsertRows(rows);
      await _loadRows(); // Reload current page
      _convertToPlutoFormat();
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void clearData() {
    _columns.clear();
    _rows.clear();
    _plutoColumns.clear();
    _plutoRows.clear();
    _currentSubSectionId = null;
    _currentPage = 0;
    _totalRows = 0;
    _searchTerm = '';
    _sortColumn = null;
    _sortAscending = true;
    notifyListeners();
  }

  // Get column by ID
  ColumnModel? getColumnById(String id) {
    try {
      return _columns.firstWhere((c) => c.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get row by ID
  RowModel? getRowById(String id) {
    try {
      return _rows.firstWhere((r) => r.id == id);
    } catch (e) {
      return null;
    }
  }
}
