import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/section_model.dart';

class SectionRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  Future<List<SectionModel>> getAllSections() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'sections',
      orderBy: 'order_index ASC',
    );

    List<SectionModel> sections = [];
    for (var map in maps) {
      final section = SectionModel.fromMap(map);
      final subSections = await getSubSectionsBySectionId(section.id);
      sections.add(section.copyWith(subSections: subSections));
    }

    return sections;
  }

  Future<SectionModel?> getSectionById(String id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'sections',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      final section = SectionModel.fromMap(maps.first);
      final subSections = await getSubSectionsBySectionId(section.id);
      return section.copyWith(subSections: subSections);
    }
    return null;
  }

  Future<String> insertSection(SectionModel section) async {
    final db = await _databaseHelper.database;
    await db.insert(
      'sections',
      section.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    return section.id;
  }

  Future<void> updateSection(SectionModel section) async {
    final db = await _databaseHelper.database;
    await db.update(
      'sections',
      section.toMap(),
      where: 'id = ?',
      whereArgs: [section.id],
    );
  }

  Future<void> deleteSection(String id) async {
    final db = await _databaseHelper.database;
    await db.delete(
      'sections',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> reorderSections(List<String> sectionIds) async {
    final db = await _databaseHelper.database;
    await db.transaction((txn) async {
      for (int i = 0; i < sectionIds.length; i++) {
        await txn.update(
          'sections',
          {'order_index': i, 'updated_at': DateTime.now().toIso8601String()},
          where: 'id = ?',
          whereArgs: [sectionIds[i]],
        );
      }
    });
  }

  Future<List<SubSectionModel>> getSubSectionsBySectionId(String sectionId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'sub_sections',
      where: 'section_id = ?',
      whereArgs: [sectionId],
      orderBy: 'order_index ASC',
    );

    return List.generate(maps.length, (i) {
      return SubSectionModel.fromMap(maps[i]);
    });
  }

  Future<SubSectionModel?> getSubSectionById(String id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'sub_sections',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return SubSectionModel.fromMap(maps.first);
    }
    return null;
  }

  Future<String> insertSubSection(SubSectionModel subSection) async {
    final db = await _databaseHelper.database;
    await db.insert(
      'sub_sections',
      subSection.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    return subSection.id;
  }

  Future<void> updateSubSection(SubSectionModel subSection) async {
    final db = await _databaseHelper.database;
    await db.update(
      'sub_sections',
      subSection.toMap(),
      where: 'id = ?',
      whereArgs: [subSection.id],
    );
  }

  Future<void> deleteSubSection(String id) async {
    final db = await _databaseHelper.database;
    await db.delete(
      'sub_sections',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> reorderSubSections(String sectionId, List<String> subSectionIds) async {
    final db = await _databaseHelper.database;
    await db.transaction((txn) async {
      for (int i = 0; i < subSectionIds.length; i++) {
        await txn.update(
          'sub_sections',
          {'order_index': i, 'updated_at': DateTime.now().toIso8601String()},
          where: 'id = ? AND section_id = ?',
          whereArgs: [subSectionIds[i], sectionId],
        );
      }
    });
  }

  Future<int> getNextSectionOrder() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('SELECT MAX(order_index) as max_order FROM sections');
    final maxOrder = result.first['max_order'] as int?;
    return (maxOrder ?? -1) + 1;
  }

  Future<int> getNextSubSectionOrder(String sectionId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT MAX(order_index) as max_order FROM sub_sections WHERE section_id = ?',
      [sectionId],
    );
    final maxOrder = result.first['max_order'] as int?;
    return (maxOrder ?? -1) + 1;
  }
}
