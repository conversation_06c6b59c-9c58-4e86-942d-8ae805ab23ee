import 'dart:convert';
import 'package:uuid/uuid.dart';

enum DataType {
  text,
  number,
  currency,
  date,
  boolean,
  email,
  phone,
  url,
}

class ColumnModel {
  final String id;
  final String subSectionId;
  final String name;
  final DataType dataType;
  final int order;
  final double width;
  final bool isRequired;
  final String? defaultValue;
  final Map<String, dynamic>? validationRules;
  final DateTime createdAt;
  final DateTime updatedAt;

  ColumnModel({
    String? id,
    required this.subSectionId,
    required this.name,
    required this.dataType,
    required this.order,
    this.width = 120.0,
    this.isRequired = false,
    this.defaultValue,
    this.validationRules,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'sub_section_id': subSectionId,
      'name': name,
      'data_type': dataType.name,
      'order_index': order,
      'width': width,
      'is_required': isRequired ? 1 : 0,
      'default_value': defaultValue,
      'validation_rules': validationRules != null ?
          validationRules.toString() : null,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory ColumnModel.fromMap(Map<String, dynamic> map) {
    return ColumnModel(
      id: map['id'],
      subSectionId: map['sub_section_id'],
      name: map['name'],
      dataType: DataType.values.firstWhere(
        (e) => e.name == map['data_type'],
        orElse: () => DataType.text,
      ),
      order: map['order_index'],
      width: map['width']?.toDouble() ?? 120.0,
      isRequired: map['is_required'] == 1,
      defaultValue: map['default_value'],
      validationRules: map['validation_rules'] != null ?
          {} : null, // TODO: Parse validation rules
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  ColumnModel copyWith({
    String? name,
    DataType? dataType,
    int? order,
    double? width,
    bool? isRequired,
    String? defaultValue,
    Map<String, dynamic>? validationRules,
    DateTime? updatedAt,
  }) {
    return ColumnModel(
      id: id,
      subSectionId: subSectionId,
      name: name ?? this.name,
      dataType: dataType ?? this.dataType,
      order: order ?? this.order,
      width: width ?? this.width,
      isRequired: isRequired ?? this.isRequired,
      defaultValue: defaultValue ?? this.defaultValue,
      validationRules: validationRules ?? this.validationRules,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }
}

class RowModel {
  final String id;
  final String subSectionId;
  final Map<String, dynamic> data;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isDeleted;
  final String? supabaseId;

  RowModel({
    String? id,
    required this.subSectionId,
    required this.data,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.isDeleted = false,
    this.supabaseId,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'sub_section_id': subSectionId,
      'data': jsonEncode(data),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_deleted': isDeleted ? 1 : 0,
      'supabase_id': supabaseId,
    };
  }

  factory RowModel.fromMap(Map<String, dynamic> map) {
    Map<String, dynamic> parsedData = {};
    try {
      if (map['data'] != null && map['data'].toString().isNotEmpty) {
        parsedData = Map<String, dynamic>.from(jsonDecode(map['data']));
      }
    } catch (e) {
      // If parsing fails, use empty map
      parsedData = {};
    }

    return RowModel(
      id: map['id'],
      subSectionId: map['sub_section_id'],
      data: parsedData,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
      isDeleted: map['is_deleted'] == 1,
      supabaseId: map['supabase_id'],
    );
  }

  RowModel copyWith({
    Map<String, dynamic>? data,
    DateTime? updatedAt,
    bool? isDeleted,
    String? supabaseId,
  }) {
    return RowModel(
      id: id,
      subSectionId: subSectionId,
      data: data ?? this.data,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      isDeleted: isDeleted ?? this.isDeleted,
      supabaseId: supabaseId ?? this.supabaseId,
    );
  }
}
