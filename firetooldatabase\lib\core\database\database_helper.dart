import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../constants/app_constants.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), AppConstants.databaseName);
    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create sections table
    await db.execute('''
      CREATE TABLE sections (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        icon_name TEXT,
        order_index INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create sub_sections table
    await db.execute('''
      CREATE TABLE sub_sections (
        id TEXT PRIMARY KEY,
        section_id TEXT NOT NULL,
        name TEXT NOT NULL,
        order_index INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (section_id) REFERENCES sections (id) ON DELETE CASCADE
      )
    ''');

    // Create columns table
    await db.execute('''
      CREATE TABLE columns (
        id TEXT PRIMARY KEY,
        sub_section_id TEXT NOT NULL,
        name TEXT NOT NULL,
        data_type TEXT NOT NULL,
        order_index INTEGER NOT NULL,
        width REAL NOT NULL DEFAULT 120.0,
        is_required INTEGER NOT NULL DEFAULT 0,
        default_value TEXT,
        validation_rules TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (sub_section_id) REFERENCES sub_sections (id) ON DELETE CASCADE
      )
    ''');

    // Create rows table
    await db.execute('''
      CREATE TABLE rows (
        id TEXT PRIMARY KEY,
        sub_section_id TEXT NOT NULL,
        data TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        supabase_id TEXT,
        FOREIGN KEY (sub_section_id) REFERENCES sub_sections (id) ON DELETE CASCADE
      )
    ''');

    // Create sync_queue table for offline sync
    await db.execute('''
      CREATE TABLE sync_queue (
        id TEXT PRIMARY KEY,
        table_name TEXT NOT NULL,
        record_id TEXT NOT NULL,
        operation TEXT NOT NULL,
        data TEXT NOT NULL,
        created_at TEXT NOT NULL,
        retry_count INTEGER NOT NULL DEFAULT 0
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_sections_order ON sections (order_index)');
    await db.execute('CREATE INDEX idx_sub_sections_section_id ON sub_sections (section_id)');
    await db.execute('CREATE INDEX idx_sub_sections_order ON sub_sections (order_index)');
    await db.execute('CREATE INDEX idx_columns_sub_section_id ON columns (sub_section_id)');
    await db.execute('CREATE INDEX idx_columns_order ON columns (order_index)');
    await db.execute('CREATE INDEX idx_rows_sub_section_id ON rows (sub_section_id)');
    await db.execute('CREATE INDEX idx_rows_is_deleted ON rows (is_deleted)');
    await db.execute('CREATE INDEX idx_sync_queue_table_record ON sync_queue (table_name, record_id)');

    // Insert default sections
    for (int i = 0; i < AppConstants.defaultSections.length; i++) {
      await db.insert('sections', {
        'id': 'section_${i + 1}',
        'name': AppConstants.defaultSections[i],
        'icon_name': _getDefaultIcon(AppConstants.defaultSections[i]),
        'order_index': i,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    }
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < 2) {
      // Add new columns or tables for version 2
    }
  }

  String _getDefaultIcon(String sectionName) {
    switch (sectionName.toLowerCase()) {
      case 'fire alarm':
        return 'alarm';
      case 'firefighting':
        return 'local_fire_department';
      case 'clean agent':
        return 'cleaning_services';
      case 'foam':
        return 'bubble_chart';
      case 'co2':
        return 'co2';
      case 'fire pumps':
        return 'water_pump';
      case 'foam pumps':
        return 'pump';
      case 'civil works':
        return 'construction';
      default:
        return 'folder';
    }
  }

  Future<void> closeDatabase() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  Future<void> deleteDatabase() async {
    String path = join(await getDatabasesPath(), AppConstants.databaseName);
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
